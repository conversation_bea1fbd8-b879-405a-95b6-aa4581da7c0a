#!/usr/bin/env python
"""
Test the embedding API specifically to understand the correct syntax.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'TomatoGuard.settings')
django.setup()

def test_embedding_api():
    """Test the embedding API with different syntaxes."""
    print("🔍 Testing Embedding API")
    print("=" * 50)
    
    # Check API key
    from django.conf import settings
    api_key = getattr(settings, 'GEMINI_API_KEY', None)
    
    if not api_key:
        print("❌ No API key found!")
        return
    
    print(f"✅ API key: {api_key[:10]}...{api_key[-5:]}")
    
    try:
        import google.generativeai as genai
        
        # Configure API
        genai.configure(api_key=api_key)
        print("✅ API configured")
        
        # Check available models
        models = list(genai.list_models())
        print(f"\nFound {len(models)} models:")
        
        embedding_model = None
        for model in models:
            print(f"  - {model.name}")
            if hasattr(model, 'supported_generation_methods'):
                methods = model.supported_generation_methods
                print(f"    Methods: {methods}")
                if 'embedText' in methods:
                    embedding_model = model.name
                    print(f"    ✅ This model supports embedText!")
        
        if not embedding_model:
            print("❌ No embedding model found!")
            return
        
        print(f"\n🧪 Testing embedding model: {embedding_model}")
        
        # Test different embedding syntaxes
        test_text = "healthy green tomato leaf with no disease symptoms"
        
        # Method 1: With model parameter
        print("\nMethod 1: genai.generate_embeddings(model=..., text=...)")
        try:
            response = genai.generate_embeddings(
                model=embedding_model,
                text=test_text
            )
            if response:
                print("✅ Method 1 WORKS!")
                print(f"Embedding length: {len(response.embedding) if hasattr(response, 'embedding') else 'unknown'}")
                print("🎯 THIS SYNTAX CAN BE USED!")
            else:
                print("❌ Method 1 returned None")
        except Exception as e:
            print(f"❌ Method 1 failed: {e}")
        
        # Method 2: With text first
        print("\nMethod 2: genai.generate_embeddings(text=..., model=...)")
        try:
            response = genai.generate_embeddings(
                text=test_text,
                model=embedding_model
            )
            if response:
                print("✅ Method 2 WORKS!")
                print(f"Embedding length: {len(response.embedding) if hasattr(response, 'embedding') else 'unknown'}")
                print("🎯 THIS SYNTAX CAN BE USED!")
            else:
                print("❌ Method 2 returned None")
        except Exception as e:
            print(f"❌ Method 2 failed: {e}")
        
        # Method 3: Without model parameter (if it has a default)
        print("\nMethod 3: genai.generate_embeddings(text=...)")
        try:
            response = genai.generate_embeddings(text=test_text)
            if response:
                print("✅ Method 3 WORKS!")
                print(f"Embedding length: {len(response.embedding) if hasattr(response, 'embedding') else 'unknown'}")
                print("🎯 THIS SYNTAX CAN BE USED!")
            else:
                print("❌ Method 3 returned None")
        except Exception as e:
            print(f"❌ Method 3 failed: {e}")
        
        # Method 4: Try with different parameter names
        print("\nMethod 4: Different parameter combinations")
        try:
            # Check what parameters the function actually accepts
            import inspect
            sig = inspect.signature(genai.generate_embeddings)
            print(f"Function signature: {sig}")
            
            # Try with 'content' instead of 'text'
            try:
                response = genai.generate_embeddings(
                    model=embedding_model,
                    content=test_text
                )
                if response:
                    print("✅ Method 4a (content parameter) WORKS!")
                    print("🎯 THIS SYNTAX CAN BE USED!")
            except Exception as e:
                print(f"❌ Method 4a failed: {e}")
                
        except Exception as e:
            print(f"❌ Method 4 inspection failed: {e}")
        
        # Method 5: Test with actual disease descriptions
        print("\nMethod 5: Testing with disease descriptions")
        disease_texts = [
            "healthy green tomato leaf with vibrant color",
            "early blight dark brown spots with concentric rings",
            "late blight water soaked lesions irregular margins"
        ]
        
        for i, disease_text in enumerate(disease_texts):
            try:
                response = genai.generate_embeddings(
                    model=embedding_model,
                    text=disease_text
                )
                if response:
                    print(f"  ✅ Disease {i+1} embedding generated!")
                else:
                    print(f"  ❌ Disease {i+1} returned None")
            except Exception as e:
                print(f"  ❌ Disease {i+1} failed: {e}")
                break
        
        print("\n" + "=" * 50)
        print("🎯 SUMMARY:")
        print("- Look for methods that show 'WORKS!' and 'CAN BE USED!'")
        print("- The working syntax will be used in the real analyzer")
        
    except Exception as e:
        print(f"❌ Failed to test embedding API: {e}")

if __name__ == '__main__':
    test_embedding_api()
