#!/usr/bin/env python
"""
Simple test for upload functionality.
"""

import os
import django
from io import BytesIO
from PIL import Image

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'TomatoGuard.settings')
django.setup()

from django.test import Client
from django.core.files.uploadedfile import SimpleUploadedFile

def create_test_image():
    """Create a simple test image."""
    img = Image.new('RGB', (300, 300), color='green')
    img_io = BytesIO()
    img.save(img_io, format='JPEG')
    img_io.seek(0)
    
    return SimpleUploadedFile(
        name='test_leaf.jpg',
        content=img_io.getvalue(),
        content_type='image/jpeg'
    )

def test_upload():
    """Test upload functionality."""
    print("🍅 Testing TomatoGuard Upload")
    print("=" * 30)
    
    client = Client()
    
    # Test upload page
    response = client.get('/upload/')
    print(f"Upload page status: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ Upload page loads successfully")
        
        # Test form submission
        test_image = create_test_image()
        
        form_data = {
            'analysis_type': 'disease_detection',
            'confidence_threshold': 75,
            'notes': 'Test upload'
        }
        
        print("Submitting form with real image...")
        response = client.post('/analyze/', {
            **form_data,
            'image': test_image
        })
        
        print(f"Analysis response status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.content.decode()
            
            # Save response for inspection
            with open('simple_test_response.html', 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Check for success indicators
            if 'Analysis Complete' in content:
                print("✅ Real AI analysis completed successfully!")
            elif 'Analysis Failed' in content:
                print("⚠️ Analysis failed but handled gracefully")
            elif 'Confidence Score' in content:
                print("✅ Analysis results displayed")
            else:
                print("⚠️ Unexpected response format")
            
            print("📄 Response saved to simple_test_response.html")
        else:
            print(f"❌ Analysis failed with status {response.status_code}")
    else:
        print(f"❌ Upload page failed with status {response.status_code}")

if __name__ == '__main__':
    test_upload()
