<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Click Behavior</title>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <style>
        .upload-area {
            border: 2px dashed #ccc;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            margin: 2rem;
            border-radius: 8px;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .hidden {
            display: none;
        }
        .log {
            margin: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Test Click-to-Browse Behavior</h1>
    
    <div x-data="testUpload()" x-init="init()">
        <div class="upload-area" 
             @click.prevent="triggerFileInput()">
            <h3>Click to Browse Files</h3>
            <p>This should only open the file dialog once per click</p>
        </div>
        
        <input type="file" 
               x-ref="fileInput" 
               @change="handleFileSelect($event)"
               class="hidden" 
               accept="image/*">
        
        <div x-show="selectedFile">
            <p>Selected file: <span x-text="selectedFile?.name"></span></p>
        </div>
        
        <div class="log">
            <h4>Event Log:</h4>
            <div x-html="logHtml"></div>
        </div>
        
        <button @click="clearLog()">Clear Log</button>
    </div>

    <script>
        function testUpload() {
            return {
                selectedFile: null,
                _clicking: false,
                logEntries: [],
                
                get logHtml() {
                    return this.logEntries.map(entry => `<div>${entry}</div>`).join('');
                },
                
                init() {
                    this.log('Component initialized');
                },
                
                triggerFileInput() {
                    this.log('triggerFileInput() called');
                    
                    // Prevent rapid successive calls
                    if (this._clicking) {
                        this.log('⚠️ Blocked duplicate click (already processing)');
                        return;
                    }
                    
                    this._clicking = true;
                    this.log('🔄 Setting _clicking = true');
                    
                    // Trigger the hidden file input
                    if (this.$refs.fileInput) {
                        this.log('📁 Triggering file input click');
                        this.$refs.fileInput.click();
                    } else {
                        this.log('❌ File input not found');
                    }
                    
                    // Reset the flag after a short delay
                    setTimeout(() => {
                        this._clicking = false;
                        this.log('✅ Reset _clicking = false');
                    }, 500);
                },
                
                handleFileSelect(event) {
                    const file = event.target.files[0];
                    if (file) {
                        this.selectedFile = file;
                        this.log(`📄 File selected: ${file.name}`);
                    } else {
                        this.log('❌ No file selected');
                    }
                },
                
                log(message) {
                    const timestamp = new Date().toLocaleTimeString();
                    this.logEntries.push(`[${timestamp}] ${message}`);
                    
                    // Keep only last 20 entries
                    if (this.logEntries.length > 20) {
                        this.logEntries = this.logEntries.slice(-20);
                    }
                },
                
                clearLog() {
                    this.logEntries = [];
                }
            }
        }
    </script>
</body>
</html>
