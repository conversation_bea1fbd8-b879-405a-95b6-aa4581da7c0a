<!-- Image Upload Form Component -->
<div class="bg-white rounded-xl shadow-lg p-8"
     x-data="uploadForm()"
     x-init="init()">

    <form hx-post="{% url 'analyzer:analyze' %}"
          hx-encoding="multipart/form-data"
          hx-target="#analysis-results"
          hx-indicator="#loading-indicator"
          hx-swap="innerHTML"
          @submit="handleSubmit"
          class="space-y-6"
          id="upload-form">

        {% csrf_token %}

        <!-- Upload Area -->
        <div class="relative">
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center transition-all duration-200 upload-area cursor-pointer"
                 :class="{ 'border-tomato-500 bg-tomato-50': isDragOver, 'border-gray-300': !isDragOver }"
                 @dragenter.prevent="isDragOver = true"
                 @dragover.prevent="isDragOver = true"
                 @dragleave.prevent="handleDragLeave($event)"
                 @drop.prevent="handleDrop($event)"
                 @click.prevent="triggerFileInput()">

                <!-- Upload Icon -->
                <div class="mx-auto w-16 h-16 mb-4 pointer-events-none">
                    <svg class="w-full h-full text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                </div>

                <!-- Upload Text -->
                <div class="space-y-2 pointer-events-none">
                    <h3 class="text-lg font-semibold text-gray-800">Upload Tomato Leaf Image</h3>
                    <p class="text-gray-600">Drag and drop your image here, or <span class="text-tomato-600 font-medium">click to browse</span></p>
                    <p class="text-sm text-gray-500">Supports: JPG, PNG, BMP, TIFF (Max 5MB)</p>
                </div>
            </div>

            <!-- Hidden File Input -->
            <input type="file"
                   name="image"
                   id="image-input"
                   x-ref="fileInput"
                   accept="image/jpeg,image/jpg,image/png,image/bmp,image/tiff"
                   @change="handleFileSelect($event)"
                   class="hidden"
                   required>

            <!-- File Preview -->
            <div x-show="selectedFile"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 transform scale-90"
                 x-transition:enter-end="opacity-100 transform scale-100"
                 class="mt-4 p-4 bg-gray-50 rounded-lg border">
                <div class="flex items-center space-x-4">
                    <!-- Preview Image -->
                    <div class="flex-shrink-0">
                        <img x-show="previewUrl"
                             :src="previewUrl"
                             :alt="selectedFile?.name"
                             class="w-20 h-20 object-cover rounded-lg border border-gray-200">
                    </div>

                    <!-- File Info -->
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-800 truncate" x-text="selectedFile?.name"></p>
                        <p class="text-sm text-gray-500" x-text="formatFileSize(selectedFile?.size)"></p>
                        <div class="mt-2">
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-tomato-500 h-2 rounded-full transition-all duration-300"
                                     :style="`width: ${uploadProgress}%`"></div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1" x-text="`${uploadProgress}% uploaded`"></p>
                        </div>
                    </div>

                    <!-- Remove Button -->
                    <button type="button"
                            @click="removeFile()"
                            class="flex-shrink-0 p-2 text-gray-400 hover:text-red-500 transition-colors">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Analysis Options -->
        <div class="space-y-4">
            <h4 class="text-lg font-semibold text-gray-800">Analysis Options</h4>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Analysis Type -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Analysis Type</label>
                    <select name="analysis_type"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tomato-500 focus:border-tomato-500">
                        <option value="disease_detection">Disease Detection</option>
                        <option value="health_assessment">Health Assessment</option>
                        <option value="detailed_analysis">Detailed Analysis</option>
                    </select>
                </div>

                <!-- Confidence Threshold -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Confidence Threshold: <span x-text="confidenceThreshold + '%'"></span>
                    </label>
                    <input type="range"
                           name="confidence_threshold"
                           min="50"
                           max="95"
                           step="5"
                           x-model="confidenceThreshold"
                           class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider">
                </div>
            </div>

            <!-- Additional Notes -->
            <div>
                <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                    Additional Notes (Optional)
                </label>
                <textarea name="notes"
                          id="notes"
                          rows="3"
                          placeholder="Describe any symptoms you've noticed or specific concerns..."
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tomato-500 focus:border-tomato-500 resize-none"></textarea>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-center">
            <button type="submit"
                    :disabled="!selectedFile || isAnalyzing"
                    class="px-8 py-3 bg-gradient-to-r from-tomato-500 to-tomato-600 text-white font-semibold rounded-lg
                           hover:from-tomato-600 hover:to-tomato-700 disabled:opacity-50 disabled:cursor-not-allowed
                           transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl
                           flex items-center space-x-3">
                <span x-show="!isAnalyzing">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </span>
                <span x-show="isAnalyzing">
                    <svg class="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </span>
                <span x-text="isAnalyzing ? 'Analyzing...' : 'Analyze Image'"></span>
            </button>
        </div>
    </form>

    <!-- Loading Indicator -->
    <div id="loading-indicator" class="htmx-indicator">
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-tomato-600"></div>
                <span class="text-gray-700 font-medium">Analyzing your image with AI...</span>
            </div>
        </div>
    </div>
</div>

<script>
function uploadForm() {
    return {
        selectedFile: null,
        previewUrl: null,
        isDragOver: false,
        uploadProgress: 0,
        isAnalyzing: false,
        confidenceThreshold: 75,
        _clicking: false,

        init() {
            // Initialize HTMX event listeners
            this.setupHTMXListeners();
        },

        triggerFileInput() {
            // Prevent rapid successive calls
            if (this._clicking) return;
            this._clicking = true;

            // Trigger the hidden file input
            if (this.$refs.fileInput) {
                this.$refs.fileInput.click();
            }

            // Reset the flag after a short delay
            setTimeout(() => {
                this._clicking = false;
            }, 500);
        },

        setupHTMXListeners() {
            // Listen for HTMX events
            document.addEventListener('htmx:beforeRequest', (event) => {
                if (event.detail.requestConfig.path.includes('analyze')) {
                    this.isAnalyzing = true;
                }
            });

            document.addEventListener('htmx:afterRequest', (event) => {
                if (event.detail.requestConfig.path.includes('analyze')) {
                    this.isAnalyzing = false;

                    if (event.detail.xhr.status === 200) {
                        // Success - scroll to results
                        setTimeout(() => {
                            const resultsElement = document.getElementById('analysis-results');
                            if (resultsElement && resultsElement.innerHTML.trim()) {
                                resultsElement.scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'start'
                                });
                            }
                        }, 100);
                    } else {
                        // Error handling
                        this.showError('Analysis failed. Please try again.');
                    }
                }
            });

            document.addEventListener('htmx:responseError', (event) => {
                if (event.detail.requestConfig.path.includes('analyze')) {
                    this.isAnalyzing = false;
                    this.showError('Server error occurred. Please try again.');
                }
            });

            document.addEventListener('htmx:sendError', (event) => {
                if (event.detail.requestConfig.path.includes('analyze')) {
                    this.isAnalyzing = false;
                    this.showError('Network error. Please check your connection.');
                }
            });
        },

        handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                this.setFile(file);
            }
        },

        handleDragLeave(event) {
            // Only set isDragOver to false if we're leaving the drop zone entirely
            const rect = event.currentTarget.getBoundingClientRect();
            const x = event.clientX;
            const y = event.clientY;

            if (x < rect.left || x >= rect.right || y < rect.top || y >= rect.bottom) {
                this.isDragOver = false;
            }
        },

        handleDrop(event) {
            event.preventDefault();
            event.stopPropagation();
            this.isDragOver = false;

            const files = event.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                // Validate file before setting
                const validation = this.validateFile(file);
                if (validation.valid) {
                    this.setFile(file);
                    // Update the file input
                    const input = this.$refs.fileInput;
                    try {
                        const dt = new DataTransfer();
                        dt.items.add(file);
                        input.files = dt.files;
                    } catch (e) {
                        // Fallback for browsers that don't support DataTransfer
                        console.warn('DataTransfer not supported, file input not updated');
                    }
                } else {
                    this.showError(validation.message);
                }
            }
        },

        validateFile(file) {
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/bmp', 'image/tiff'];
            const maxSize = 5 * 1024 * 1024; // 5MB

            if (!allowedTypes.includes(file.type)) {
                return { valid: false, message: 'Please select a valid image file (JPG, PNG, BMP, TIFF)' };
            }

            if (file.size > maxSize) {
                return { valid: false, message: 'File size must be less than 5MB' };
            }

            return { valid: true };
        },

        setFile(file) {
            const validation = this.validateFile(file);
            if (!validation.valid) {
                this.showError(validation.message);
                return;
            }

            this.selectedFile = file;
            this.uploadProgress = 100; // Simulate immediate upload for preview

            // Create preview URL
            const reader = new FileReader();
            reader.onload = (e) => {
                this.previewUrl = e.target.result;
            };
            reader.readAsDataURL(file);
        },

        showError(message) {
            if (window.TomatoGuard && window.TomatoGuard.utils) {
                window.TomatoGuard.utils.showNotification(message, 'error');
            } else {
                alert(message);
            }
        },

        removeFile() {
            this.selectedFile = null;
            this.previewUrl = null;
            this.uploadProgress = 0;
            this.$refs.fileInput.value = '';
        },

        handleSubmit(event) {
            if (!this.selectedFile) {
                event.preventDefault();
                this.showError('Please select an image to analyze');
                return false;
            }

            // Validate file again before submission
            const validation = this.validateFile(this.selectedFile);
            if (!validation.valid) {
                event.preventDefault();
                this.showError(validation.message);
                return false;
            }

            this.isAnalyzing = true;

            // Reset any previous results
            const resultsContainer = document.getElementById('analysis-results');
            if (resultsContainer) {
                resultsContainer.innerHTML = '';
            }

            return true;
        },

        formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    }
}
</script>
