#!/usr/bin/env python
"""
Test Gemini API functionality to understand the available methods.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'TomatoGuard.settings')
django.setup()

def test_gemini_imports():
    """Test what's available in the google-generativeai package."""
    print("🔍 Testing Gemini API imports...")
    
    try:
        import google.generativeai as genai
        print("✅ google.generativeai imported successfully")
        
        # Check available attributes
        print("\n📋 Available attributes in genai:")
        for attr in dir(genai):
            if not attr.startswith('_'):
                print(f"  - {attr}")
        
        # Try to configure with API key
        from django.conf import settings
        api_key = getattr(settings, 'GEMINI_API_KEY', None)
        
        if api_key and api_key != 'your-api-key-here':
            print(f"\n🔑 Configuring with API key: {api_key[:10]}...")
            genai.configure(api_key=api_key)
            print("✅ API key configured")
            
            # Try to list models
            try:
                print("\n📋 Available models:")
                for model in genai.list_models():
                    print(f"  - {model.name}")
            except Exception as e:
                print(f"❌ Failed to list models: {e}")
            
            # Try to create a model
            try:
                model = genai.GenerativeModel('gemini-pro')
                print("✅ GenerativeModel created successfully")
                
                # Try a simple text generation
                response = model.generate_content("Hello, how are you?")
                print(f"✅ Text generation works: {response.text[:50]}...")
                
            except Exception as e:
                print(f"❌ Failed to create GenerativeModel: {e}")
                
                # Try alternative approaches
                try:
                    print("\n🔄 Trying alternative API approaches...")
                    
                    # Check if there's a different way to access models
                    if hasattr(genai, 'chat'):
                        print("✅ genai.chat available")
                    
                    if hasattr(genai, 'generate_text'):
                        print("✅ genai.generate_text available")
                        response = genai.generate_text(prompt="Hello, how are you?")
                        print(f"✅ generate_text works: {response}")
                    
                except Exception as e2:
                    print(f"❌ Alternative approaches failed: {e2}")
        else:
            print("⚠️ No valid API key found")
            
    except ImportError as e:
        print(f"❌ Failed to import google.generativeai: {e}")
    
    # Try the older API
    try:
        import google.ai.generativelanguage as glm
        print("\n✅ google.ai.generativelanguage imported successfully")
        
        print("📋 Available attributes in glm:")
        for attr in dir(glm):
            if not attr.startswith('_'):
                print(f"  - {attr}")
                
    except ImportError as e:
        print(f"\n❌ Failed to import google.ai.generativelanguage: {e}")

if __name__ == '__main__':
    test_gemini_imports()
