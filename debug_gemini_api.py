#!/usr/bin/env python
"""
Debug Gemini API to understand the 404 error.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'TomatoGuard.settings')
django.setup()

def debug_gemini_api():
    """Debug the Gemini API integration."""
    print("🔍 Debugging Gemini API Integration")
    print("=" * 50)
    
    # Check API key
    from django.conf import settings
    api_key = getattr(settings, 'GEMINI_API_KEY', None)
    
    print(f"API Key: {api_key[:10]}...{api_key[-5:]}" if api_key else "No API key")
    
    try:
        import google.generativeai as genai
        print("✅ google.generativeai imported")
        
        # Configure API
        genai.configure(api_key=api_key)
        print("✅ API configured")
        
        # Check available models
        print("\n📋 Checking available models...")
        try:
            models = list(genai.list_models())
            print(f"Found {len(models)} models:")
            for model in models[:5]:  # Show first 5
                print(f"  - {model.name}")
        except Exception as e:
            print(f"❌ Failed to list models: {e}")
        
        # Test different API approaches
        print("\n🧪 Testing different API approaches...")
        
        # Test 1: Simple generate_text
        try:
            print("Test 1: generate_text with simple prompt...")
            response = genai.generate_text(prompt="Hello, respond with just 'API working'")
            if response and response.result:
                print(f"✅ generate_text works: {response.result}")
            else:
                print("❌ generate_text returned empty response")
        except Exception as e:
            print(f"❌ generate_text failed: {e}")
        
        # Test 2: Try with different model
        try:
            print("\nTest 2: Trying with specific model...")
            response = genai.generate_text(
                model='models/text-bison-001',
                prompt="Hello, respond with just 'API working'"
            )
            if response and response.result:
                print(f"✅ text-bison-001 works: {response.result}")
            else:
                print("❌ text-bison-001 returned empty response")
        except Exception as e:
            print(f"❌ text-bison-001 failed: {e}")
        
        # Test 3: Try newer API approach
        try:
            print("\nTest 3: Trying GenerativeModel approach...")
            model = genai.GenerativeModel('gemini-pro')
            response = model.generate_content("Hello, respond with just 'API working'")
            if response and response.text:
                print(f"✅ GenerativeModel works: {response.text}")
            else:
                print("❌ GenerativeModel returned empty response")
        except Exception as e:
            print(f"❌ GenerativeModel failed: {e}")
            
    except Exception as e:
        print(f"❌ Failed to import or configure: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Debug Summary:")
    print("- Check which test passes to determine the correct API approach")
    print("- If all fail, the API key might need different permissions")

if __name__ == '__main__':
    debug_gemini_api()
