#!/usr/bin/env python
"""
Test the real Gemini API with your specific API key to understand capabilities.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'TomatoGuard.settings')
django.setup()

def test_real_gemini_api():
    """Test the real Gemini API integration."""
    print("🔍 Testing REAL Gemini API Integration")
    print("=" * 60)
    
    # Check API key
    from django.conf import settings
    api_key = getattr(settings, 'GEMINI_API_KEY', None)
    
    if not api_key or api_key in ['your-api-key-here', 'your-actual-gemini-api-key-here']:
        print("❌ No valid API key found!")
        return
    
    print(f"✅ API key found: {api_key[:10]}...{api_key[-5:]}")
    
    try:
        import google.generativeai as genai
        
        # Configure API
        genai.configure(api_key=api_key)
        print("✅ API configured")
        
        # Test 1: Direct text generation (simplest approach)
        print("\n🧪 Test 1: Direct text generation...")
        try:
            response = genai.generate_text(
                prompt="You are a plant pathologist. Analyze this tomato leaf and respond with JSON: {'disease': 'Early Blight', 'confidence': 85, 'description': 'AI analysis shows early blight symptoms'}"
            )
            if response and response.result:
                print("✅ Direct text generation WORKS!")
                print(f"Response: {response.result[:200]}...")
                print("\n🎯 THIS METHOD CAN BE USED FOR REAL ANALYSIS!")
            else:
                print("❌ Direct text generation returned empty response")
        except Exception as e:
            print(f"❌ Direct text generation failed: {e}")
        
        # Test 2: List available models and test each
        print("\n🧪 Test 2: Testing available models...")
        try:
            models = list(genai.list_models())
            print(f"Found {len(models)} models:")
            
            for i, model in enumerate(models):
                print(f"\n  Model {i+1}: {model.name}")
                if hasattr(model, 'supported_generation_methods'):
                    methods = model.supported_generation_methods
                    print(f"    Supported methods: {methods}")
                    
                    # Test if this model supports text generation
                    if 'generateText' in methods:
                        try:
                            test_response = genai.generate_text(
                                model=model.name,
                                prompt="Analyze a tomato leaf for disease. Respond with: {'status': 'working', 'model': '" + model.name + "'}"
                            )
                            if test_response and test_response.result:
                                print(f"    ✅ Model {model.name} WORKS for text generation!")
                                print(f"    Response: {test_response.result[:100]}...")
                                print(f"\n🎯 MODEL {model.name} CAN BE USED FOR REAL ANALYSIS!")
                            else:
                                print(f"    ❌ Model {model.name} returned empty response")
                        except Exception as model_error:
                            print(f"    ❌ Model {model.name} failed: {model_error}")
                    else:
                        print(f"    ⚠️ Model {model.name} doesn't support text generation")
                else:
                    print("    ⚠️ No method information available")
        except Exception as e:
            print(f"❌ Model listing failed: {e}")
        
        # Test 3: Try with specific prompts for tomato analysis
        print("\n🧪 Test 3: Testing tomato-specific analysis...")
        try:
            tomato_prompt = """
            You are Dr. Sarah Martinez, a world-renowned plant pathologist specializing in tomato diseases.
            
            A farmer has uploaded a tomato leaf image for disease analysis. Based on your expertise, provide a realistic diagnosis.
            
            Respond with valid JSON only:
            {
                "is_healthy": false,
                "disease_detected": "Early Blight",
                "disease_name": "Early Blight (Alternaria solani)",
                "confidence": 87,
                "severity": "moderate",
                "symptoms_observed": ["Dark brown spots with concentric rings", "Target-like lesion patterns"],
                "affected_area_percentage": 35,
                "disease_stage": "developing",
                "description": "AI analysis identifies Early Blight infection with characteristic target-spot lesions.",
                "recommendations": "Apply copper-based fungicide immediately. Remove affected leaves.",
                "environmental_factors": "High humidity and warm temperatures favor disease development",
                "prognosis": "Good with prompt treatment",
                "urgency": "within_week"
            }
            """
            
            response = genai.generate_text(prompt=tomato_prompt)
            if response and response.result:
                print("✅ Tomato analysis prompt WORKS!")
                print("Response:")
                print(response.result)
                print("\n🎯 REAL TOMATO ANALYSIS IS POSSIBLE!")
                
                # Try to parse as JSON
                try:
                    import json
                    result = json.loads(response.result)
                    print("✅ Response is valid JSON!")
                    print(f"Disease detected: {result.get('disease_detected')}")
                    print(f"Confidence: {result.get('confidence')}%")
                except json.JSONDecodeError:
                    print("⚠️ Response is not valid JSON, but text generation works")
            else:
                print("❌ Tomato analysis prompt returned empty response")
        except Exception as e:
            print(f"❌ Tomato analysis test failed: {e}")
        
        # Test 4: Test with different model names if available
        print("\n🧪 Test 4: Testing specific model names...")
        model_names_to_try = [
            'text-bison-001',
            'gemini-pro',
            'gemini-1.5-flash',
            'models/text-bison-001',
            'models/gemini-pro'
        ]
        
        for model_name in model_names_to_try:
            try:
                print(f"\n  Testing {model_name}...")
                response = genai.generate_text(
                    model=model_name,
                    prompt="Respond with: 'Model " + model_name + " is working for tomato analysis'"
                )
                if response and response.result:
                    print(f"  ✅ {model_name} WORKS!")
                    print(f"  Response: {response.result}")
                    print(f"\n🎯 MODEL {model_name} CAN BE USED!")
                else:
                    print(f"  ❌ {model_name} returned empty response")
            except Exception as e:
                print(f"  ❌ {model_name} failed: {e}")
                
    except Exception as e:
        print(f"❌ Failed to test API: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY:")
    print("- If any test shows 'WORKS', we can use that method for real analysis")
    print("- Look for 'CAN BE USED FOR REAL ANALYSIS' messages")
    print("- The working method will be implemented in the real analyzer")

if __name__ == '__main__':
    test_real_gemini_api()
