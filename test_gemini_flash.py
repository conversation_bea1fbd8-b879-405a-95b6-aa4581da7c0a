#!/usr/bin/env python
"""
Test gemini-1.5-flash model specifically.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'TomatoGuard.settings')
django.setup()

def test_gemini_flash():
    """Test gemini-1.5-flash model."""
    print("🔍 Testing gemini-1.5-flash Model")
    print("=" * 50)
    
    # Check API key
    from django.conf import settings
    api_key = getattr(settings, 'GEMINI_API_KEY', None)
    
    if not api_key or api_key in ['your-api-key-here', 'your-actual-gemini-api-key-here']:
        print("❌ No valid API key found!")
        return
    
    print(f"API Key: {api_key[:10]}...{api_key[-5:]}")
    
    try:
        import google.generativeai as genai
        
        # Configure API
        genai.configure(api_key=api_key)
        print("✅ API configured")
        
        # Test 1: Try generate_text with gemini-1.5-flash
        try:
            print("\nTest 1: generate_text with gemini-1.5-flash...")
            response = genai.generate_text(
                model='gemini-1.5-flash',
                prompt="You are an expert plant pathologist. Analyze a tomato leaf and respond with JSON: {'disease': 'Early Blight', 'confidence': 85}"
            )
            if response and response.result:
                print(f"✅ gemini-1.5-flash works with generate_text!")
                print(f"Response: {response.result[:200]}...")
            else:
                print("❌ Empty response from gemini-1.5-flash")
        except Exception as e:
            print(f"❌ gemini-1.5-flash with generate_text failed: {e}")
        
        # Test 2: Try with models/ prefix
        try:
            print("\nTest 2: generate_text with models/gemini-1.5-flash...")
            response = genai.generate_text(
                model='models/gemini-1.5-flash',
                prompt="You are an expert plant pathologist. Analyze a tomato leaf and respond with JSON: {'disease': 'Early Blight', 'confidence': 85}"
            )
            if response and response.result:
                print(f"✅ models/gemini-1.5-flash works!")
                print(f"Response: {response.result[:200]}...")
            else:
                print("❌ Empty response from models/gemini-1.5-flash")
        except Exception as e:
            print(f"❌ models/gemini-1.5-flash failed: {e}")
        
        # Test 3: Try GenerativeModel if available
        try:
            print("\nTest 3: GenerativeModel approach...")
            if hasattr(genai, 'GenerativeModel'):
                model = genai.GenerativeModel('gemini-1.5-flash')
                response = model.generate_content("Hello, respond with 'API working'")
                if response and response.text:
                    print(f"✅ GenerativeModel works!")
                    print(f"Response: {response.text}")
                else:
                    print("❌ Empty response from GenerativeModel")
            else:
                print("❌ GenerativeModel not available in this version")
        except Exception as e:
            print(f"❌ GenerativeModel failed: {e}")
        
        # Test 4: List available models again
        try:
            print("\nTest 4: Checking all available models...")
            models = list(genai.list_models())
            print(f"Total models: {len(models)}")
            for model in models:
                print(f"  - {model.name}")
                if hasattr(model, 'supported_generation_methods'):
                    print(f"    Methods: {model.supported_generation_methods}")
        except Exception as e:
            print(f"❌ Failed to list models: {e}")
            
    except Exception as e:
        print(f"❌ Failed to import or configure: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Test Summary:")
    print("- If any test passes, we can use that method for real AI analysis")
    print("- If all fail, the API key may not have access to text generation models")

if __name__ == '__main__':
    test_gemini_flash()
