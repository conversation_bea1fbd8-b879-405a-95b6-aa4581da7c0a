<!-- Analysis Results Component -->
{% if analysis_result %}
    {% if analysis_result.error_message %}
        <!-- Error Display -->
        <div class="bg-red-50 border border-red-200 rounded-xl p-6 text-center animate-fade-in">
            <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-red-800 mb-2">Analysis Failed</h3>
            <p class="text-red-700 mb-4">{{ analysis_result.error_message }}</p>
            <a href="{% url 'analyzer:upload' %}"
               class="inline-flex items-center space-x-2 px-4 py-2 bg-tomato-600 text-white rounded-lg hover:bg-tomato-700 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                <span>Try Again</span>
            </a>
        </div>
    {% else %}
    <div class="bg-white rounded-xl shadow-lg overflow-hidden animate-fade-in">
        <!-- Header -->
        <div class="bg-gradient-to-r from-tomato-500 to-leaf-500 px-6 py-4">
            <h3 class="text-xl font-bold text-white flex items-center space-x-2">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Analysis Complete</span>
            </h3>
            <p class="text-white text-opacity-90 text-sm mt-1">
                Analysis completed on {{ analysis_result.created_at|date:"M d, Y \a\t H:i" }}
            </p>
        </div>

        <div class="p-6 space-y-6">
            <!-- Disease Status -->
            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    {% if analysis_result.is_healthy %}
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    {% else %}
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    {% endif %}
                </div>

                <div class="flex-1">
                    {% if analysis_result.is_healthy %}
                        <h4 class="text-xl font-semibold text-green-700 mb-2">Healthy Plant Detected! 🌱</h4>
                        <p class="text-green-600">
                            Great news! Your tomato plant appears to be healthy with no signs of disease detected.
                        </p>
                    {% else %}
                        <h4 class="text-xl font-semibold text-red-700 mb-2">Disease Detected ⚠️</h4>
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <h5 class="font-semibold text-red-800 mb-1">Identified Disease:</h5>
                            <p class="text-red-700 text-lg">{{ analysis_result.disease_name }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Confidence Score -->
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex justify-between items-center mb-2">
                    <h5 class="font-semibold text-gray-800">Confidence Score</h5>
                    <span class="text-lg font-bold
                                {% if analysis_result.confidence >= 80 %}text-green-600
                                {% elif analysis_result.confidence >= 60 %}text-yellow-600
                                {% else %}text-red-600{% endif %}">
                        {{ analysis_result.confidence }}%
                    </span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                    <div class="h-3 rounded-full transition-all duration-1000
                               {% if analysis_result.confidence >= 80 %}bg-green-500
                               {% elif analysis_result.confidence >= 60 %}bg-yellow-500
                               {% else %}bg-red-500{% endif %}"
                         style="width: {{ analysis_result.confidence }}%"></div>
                </div>
                <p class="text-sm text-gray-600 mt-2">
                    {% if analysis_result.confidence >= 80 %}
                        High confidence - Results are very reliable
                    {% elif analysis_result.confidence >= 60 %}
                        Moderate confidence - Consider additional analysis
                    {% else %}
                        Low confidence - Manual inspection recommended
                    {% endif %}
                </p>
            </div>

            <!-- Disease Stage and Affected Area -->
            {% if not analysis_result.is_healthy %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                {% if analysis_result.disease_stage %}
                <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <h5 class="font-semibold text-orange-800 mb-2">Disease Stage</h5>
                    <p class="text-orange-700 capitalize">{{ analysis_result.disease_stage }}</p>
                </div>
                {% endif %}

                {% if analysis_result.affected_area_percentage %}
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <h5 class="font-semibold text-purple-800 mb-2">Affected Area</h5>
                    <p class="text-purple-700">{{ analysis_result.affected_area_percentage }}% of leaf area</p>
                </div>
                {% endif %}
            </div>
            {% endif %}

            <!-- Symptoms Observed -->
            {% if analysis_result.symptoms_observed %}
            <div>
                <h5 class="font-semibold text-gray-800 mb-3">Symptoms Observed</h5>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <ul class="list-disc list-inside space-y-1 text-yellow-800">
                        {% for symptom in analysis_result.symptoms_observed %}
                        <li>{{ symptom }}</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
            {% endif %}

            <!-- Description -->
            {% if analysis_result.description %}
            <div>
                <h5 class="font-semibold text-gray-800 mb-3">Detailed Analysis</h5>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <p class="text-blue-800 leading-relaxed">{{ analysis_result.description }}</p>
                </div>
            </div>
            {% endif %}

            <!-- Recommendations -->
            {% if analysis_result.recommendations %}
            <div>
                <h5 class="font-semibold text-gray-800 mb-3">Recommendations</h5>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <svg class="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <p class="text-yellow-800 leading-relaxed">{{ analysis_result.recommendations }}</p>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Environmental Factors -->
            {% if analysis_result.environmental_factors %}
            <div>
                <h5 class="font-semibold text-gray-800 mb-3">Environmental Factors</h5>
                <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <svg class="w-5 h-5 text-indigo-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"></path>
                        </svg>
                        <p class="text-indigo-800 leading-relaxed">{{ analysis_result.environmental_factors }}</p>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Prognosis and Urgency -->
            {% if analysis_result.prognosis or analysis_result.urgency %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                {% if analysis_result.prognosis %}
                <div class="bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <h5 class="font-semibold text-teal-800 mb-2">Prognosis</h5>
                    <p class="text-teal-700">{{ analysis_result.prognosis }}</p>
                </div>
                {% endif %}

                {% if analysis_result.urgency %}
                <div class="{% if analysis_result.urgency == 'immediate' %}bg-red-50 border-red-200{% elif analysis_result.urgency == 'within_week' %}bg-orange-50 border-orange-200{% else %}bg-green-50 border-green-200{% endif %} border rounded-lg p-4">
                    <h5 class="font-semibold {% if analysis_result.urgency == 'immediate' %}text-red-800{% elif analysis_result.urgency == 'within_week' %}text-orange-800{% else %}text-green-800{% endif %} mb-2">Treatment Urgency</h5>
                    <p class="{% if analysis_result.urgency == 'immediate' %}text-red-700{% elif analysis_result.urgency == 'within_week' %}text-orange-700{% else %}text-green-700{% endif %} capitalize">
                        {% if analysis_result.urgency == 'immediate' %}🚨 Immediate Action Required
                        {% elif analysis_result.urgency == 'within_week' %}⚠️ Treat Within a Week
                        {% elif analysis_result.urgency == 'routine' %}✅ Routine Monitoring
                        {% else %}{{ analysis_result.urgency|title }}{% endif %}
                    </p>
                </div>
                {% endif %}
            </div>
            {% endif %}

            <!-- Uploaded Image -->
            {% if analysis_result.image %}
            <div>
                <h5 class="font-semibold text-gray-800 mb-3">Analyzed Image</h5>
                <div class="relative group">
                    <img src="{{ analysis_result.image.url }}"
                         alt="Analyzed tomato leaf"
                         class="w-full max-w-md mx-auto rounded-lg shadow-md cursor-pointer transition-transform duration-200 hover:scale-105"
                         onclick="openImageModal('{{ analysis_result.image.url }}', 'Analyzed Image')">
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center">
                        <svg class="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
                        </svg>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="flex flex-wrap gap-3 pt-4 border-t border-gray-200">
                <button onclick="window.print()"
                        class="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                    </svg>
                    <span>Print Results</span>
                </button>

                <button onclick="shareResults()"
                        class="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                    </svg>
                    <span>Share</span>
                </button>

                <a href="{% url 'analyzer:upload' %}"
                   class="flex items-center space-x-2 px-4 py-2 bg-tomato-600 text-white rounded-lg hover:bg-tomato-700 transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M12 4v16m8-8H4"></path>
                    </svg>
                    <span>Analyze Another</span>
                </a>
            </div>
        </div>
    </div>
    {% endif %}
{% endif %}

<script>
function openImageModal(imageUrl, imageName) {
    window.dispatchEvent(new CustomEvent('open-image-modal', {
        detail: { url: imageUrl, name: imageName }
    }));
}

function shareResults() {
    if (navigator.share) {
        navigator.share({
            title: 'TomatoGuard Analysis Results',
            text: 'Check out my tomato plant analysis results from TomatoGuard AI!',
            url: window.location.href
        });
    } else {
        // Fallback for browsers that don't support Web Share API
        const url = window.location.href;
        navigator.clipboard.writeText(url).then(() => {
            alert('Results URL copied to clipboard!');
        });
    }
}
</script>
