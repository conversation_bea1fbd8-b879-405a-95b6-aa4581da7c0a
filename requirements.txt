# Core Django
Django>=4.2,<5.0
django-cors-headers>=4.0.0

# Environment management
python-dotenv>=1.0.0

# Image processing
Pillow>=10.0.0

# Google AI
google-generativeai==0.1.0rc1

# Database (optional - for PostgreSQL in production)
# psycopg2-binary>=2.9.0

# Development tools (optional)
# django-debug-toolbar>=4.0.0
# django-extensions>=3.2.0

# Production server (optional)
# gunicorn>=21.0.0
# whitenoise>=6.5.0

# Testing (optional)
# pytest>=7.0.0
# pytest-django>=4.5.0
# coverage>=7.0.0
