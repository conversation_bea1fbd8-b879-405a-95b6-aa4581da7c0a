# 🍅 TomatoGuard Real AI Analysis Setup

## 🎯 Overview

TomatoGuard can use **real Google Gemini AI** for tomato leaf disease analysis. Follow this guide to enable real AI analysis instead of mock results.

## 🔑 Step 1: Get Your Gemini API Key

1. **Visit Google AI Studio**: https://aistudio.google.com/app/apikey
2. **Sign in** with your Google account
3. **Create API Key**: Click "Create API Key" button
4. **Copy the key**: It will look like `AIzaSyABC123...`

## ⚙️ Step 2: Configure TomatoGuard

1. **Open your `.env` file** in the TomatoGuard directory
2. **Update the API key**:
   ```
   GEMINI_API_KEY=AIzaSyABC123_your_actual_api_key_here
   ```
3. **Save the file**

## 🧪 Step 3: Test the Integration

Run the test script to verify everything works:

```bash
python test_real_gemini.py
```

**Expected output for success:**
```
✅ API key found: AIzaSyABC1...
✅ Gemini API configured
✅ Gemini API call successful!
✅ TomatoGuard analysis successful!
✅ Real AI analysis completed!
```

## 🚀 Step 4: Use Real AI Analysis

1. **Start the server**: `python manage.py runserver`
2. **Upload an image**: Go to http://127.0.0.1:8000/upload/
3. **Check the logs**: Look for these messages:
   - `🚀 Sending request to Gemini API for REAL AI analysis...`
   - `🎯 REAL AI analysis completed`

## 🔍 How to Verify Real AI is Working

### ✅ Success Indicators:
- Server logs show: `🚀 Sending request to Gemini API for REAL AI analysis...`
- Analysis results are varied and detailed
- No "fallback_note" in the response
- Results include `"analysis_mode": "real_ai"`

### ❌ Mock Mode Indicators:
- Server logs show: `🔄 Using mock analysis - no valid API key configured`
- Results include `"analysis_mode": "mock"`
- Consistent mock results from predefined scenarios

## 💰 Pricing Information

- **Free Tier**: Generous free usage limits
- **Cost**: Very low cost per request (fractions of a cent)
- **Limits**: Check current limits at https://ai.google.dev/gemini-api/docs/pricing

## 🛠️ Troubleshooting

### Problem: "API key not found"
**Solution**: Make sure your `.env` file has the correct API key format

### Problem: "404 Requested entity was not found"
**Solutions**:
1. Verify your API key is correct
2. Check if your API key has proper permissions
3. Try creating a new API key

### Problem: "Empty response from Gemini API"
**Solutions**:
1. Check your internet connection
2. Verify API key hasn't expired
3. Check Google AI Studio for any service issues

### Problem: Still seeing mock results
**Solutions**:
1. Restart the Django server after updating `.env`
2. Check the server logs for error messages
3. Run `python test_real_gemini.py` to debug

## 📊 Real AI vs Mock Analysis

| Feature | Mock Analysis | Real AI Analysis |
|---------|---------------|------------------|
| **Accuracy** | Predefined scenarios | AI-powered analysis |
| **Variety** | Limited patterns | Unlimited variety |
| **Learning** | Static responses | Learns from vast data |
| **Cost** | Free | Very low cost |
| **Speed** | Instant | ~2-3 seconds |

## 🎓 Educational Value

Real AI analysis provides:
- **Scientific accuracy** based on vast agricultural knowledge
- **Detailed explanations** of disease symptoms
- **Professional recommendations** for treatment
- **Environmental factor analysis**
- **Realistic confidence scoring**

## 🔒 Security Notes

- **Keep your API key secure** - don't share it publicly
- **Use environment variables** - never hardcode keys in source code
- **Monitor usage** - check your Google Cloud console for usage

## 📞 Support

If you need help:
1. Check the troubleshooting section above
2. Run the test script: `python test_real_gemini.py`
3. Check server logs for detailed error messages
4. Visit Google AI Studio documentation

---

**🎉 Once configured, TomatoGuard will provide professional-grade tomato disease analysis powered by Google's Gemini AI!**
