#!/usr/bin/env python
"""
Test real Gemini API integration for tomato leaf analysis.
"""

import os
import sys
import django
from io import BytesIO
from PIL import Image

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'TomatoGuard.settings')
django.setup()

def test_real_gemini_api():
    """Test the real Gemini API integration."""
    print("🍅 Testing Real Gemini API Integration")
    print("=" * 50)
    
    # Check API key
    from django.conf import settings
    api_key = getattr(settings, 'GEMINI_API_KEY', None)
    
    if not api_key or api_key == 'your-actual-gemini-api-key-here':
        print("❌ No valid API key found!")
        print("Please update your .env file with a real Gemini API key:")
        print("1. Go to https://aistudio.google.com/app/apikey")
        print("2. Create a new API key")
        print("3. Update .env file: GEMINI_API_KEY=your-actual-key")
        return
    
    print(f"✅ API key found: {api_key[:10]}...")
    
    # Test Gemini API directly
    try:
        import google.generativeai as genai
        
        # Configure API
        genai.configure(api_key=api_key)
        print("✅ Gemini API configured")
        
        # Test simple text generation
        test_prompt = """
        You are an expert plant pathologist. Analyze a tomato leaf for diseases.
        
        Provide a realistic analysis in JSON format:
        {
            "is_healthy": false,
            "disease_detected": "Early Blight",
            "disease_name": "Early Blight (Alternaria solani)",
            "confidence": 85,
            "severity": "moderate",
            "symptoms_observed": ["dark spots with concentric rings", "yellowing around lesions"],
            "affected_area_percentage": 25,
            "disease_stage": "developing",
            "description": "Characteristic early blight symptoms observed",
            "recommendations": "Apply copper-based fungicide",
            "environmental_factors": "High humidity conditions",
            "prognosis": "Good with treatment",
            "urgency": "within_week"
        }
        
        Respond with ONLY the JSON, no additional text.
        """
        
        print("🔄 Testing Gemini text generation...")
        response = genai.generate_text(prompt=test_prompt)
        
        if response and response.result:
            print("✅ Gemini API call successful!")
            print(f"Response length: {len(response.result)} characters")
            print(f"Response preview: {response.result[:200]}...")
            
            # Try to parse as JSON
            import json
            try:
                parsed = json.loads(response.result)
                print("✅ Response is valid JSON")
                print(f"Disease detected: {parsed.get('disease_detected', 'Unknown')}")
                print(f"Confidence: {parsed.get('confidence', 0)}%")
            except json.JSONDecodeError as e:
                print(f"⚠️ Response is not valid JSON: {e}")
                print("This is normal - Gemini sometimes adds extra text")
        else:
            print("❌ Empty response from Gemini API")
            
    except Exception as e:
        print(f"❌ Gemini API test failed: {str(e)}")
        return
    
    # Test with TomatoGuard analyzer
    print("\n🔄 Testing TomatoGuard analyzer...")
    try:
        from analyzer.utils.gemini import analyze_tomato_leaf
        from django.core.files.uploadedfile import SimpleUploadedFile
        
        # Create a test image
        img = Image.new('RGB', (300, 300), color='green')
        img_io = BytesIO()
        img.save(img_io, format='JPEG')
        img_io.seek(0)
        
        test_image = SimpleUploadedFile(
            name='test_leaf.jpg',
            content=img_io.getvalue(),
            content_type='image/jpeg'
        )
        
        # Analyze with TomatoGuard
        result = analyze_tomato_leaf(test_image)
        
        if result.get('error'):
            print(f"❌ TomatoGuard analysis failed: {result.get('error_message')}")
        else:
            print("✅ TomatoGuard analysis successful!")
            print(f"Healthy: {result.get('is_healthy')}")
            print(f"Disease: {result.get('disease_detected', 'None')}")
            print(f"Confidence: {result.get('confidence', 0)}%")
            
            if result.get('fallback_note'):
                print(f"⚠️ Fallback used: {result.get('fallback_note')}")
            else:
                print("✅ Real AI analysis completed!")
                
    except Exception as e:
        print(f"❌ TomatoGuard test failed: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🎯 Test Summary:")
    print("- If you see 'Real AI analysis completed!', the API is working")
    print("- If you see 'Fallback used', check your API key")
    print("- Make sure to use a valid Gemini API key from Google AI Studio")

if __name__ == '__main__':
    test_real_gemini_api()
