<!-- Analysis Results Component -->

    
    <div class="bg-white rounded-xl shadow-lg overflow-hidden animate-fade-in">
        <!-- Header -->
        <div class="bg-gradient-to-r from-tomato-500 to-leaf-500 px-6 py-4">
            <h3 class="text-xl font-bold text-white flex items-center space-x-2">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Analysis Complete</span>
            </h3>
            <p class="text-white text-opacity-90 text-sm mt-1">
                Analysis completed on May 30, 2025 at 23:59
            </p>
        </div>

        <div class="p-6 space-y-6">
            <!-- Disease Status -->
            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    
                </div>

                <div class="flex-1">
                    
                        <h4 class="text-xl font-semibold text-red-700 mb-2">Disease Detected ⚠️</h4>
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <h5 class="font-semibold text-red-800 mb-1">Identified Disease:</h5>
                            <p class="text-red-700 text-lg">Early Blight</p>
                        </div>
                    
                </div>
            </div>

            <!-- Confidence Score -->
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex justify-between items-center mb-2">
                    <h5 class="font-semibold text-gray-800">Confidence Score</h5>
                    <span class="text-lg font-bold
                                text-green-600
                                ">
                        87%
                    </span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                    <div class="h-3 rounded-full transition-all duration-1000
                               bg-green-500
                               "
                         style="width: 87%"></div>
                </div>
                <p class="text-sm text-gray-600 mt-2">
                    
                        High confidence - Results are very reliable
                    
                </p>
            </div>

            <!-- Disease Stage and Affected Area -->
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                
                <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <h5 class="font-semibold text-orange-800 mb-2">Disease Stage</h5>
                    <p class="text-orange-700 capitalize">developing</p>
                </div>
                

                
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <h5 class="font-semibold text-purple-800 mb-2">Affected Area</h5>
                    <p class="text-purple-700">35% of leaf area</p>
                </div>
                
            </div>
            

            <!-- Symptoms Observed -->
            
            <div>
                <h5 class="font-semibold text-gray-800 mb-3">Symptoms Observed</h5>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <ul class="list-disc list-inside space-y-1 text-yellow-800">
                        
                        <li>Dark brown spots with concentric rings</li>
                        
                        <li>Target-like lesion patterns</li>
                        
                        <li>Yellow halos around spots</li>
                        
                        <li>Lower leaf involvement</li>
                        
                    </ul>
                </div>
            </div>
            

            <!-- Description -->
            
            <div>
                <h5 class="font-semibold text-gray-800 mb-3">Detailed Analysis</h5>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <p class="text-blue-800 leading-relaxed">AI analysis indicates Early Blight infection with characteristic target-spot lesions. The concentric ring pattern is diagnostic of Alternaria solani.</p>
                </div>
            </div>
            

            <!-- Recommendations -->
            
            <div>
                <h5 class="font-semibold text-gray-800 mb-3">Recommendations</h5>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <svg class="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <p class="text-yellow-800 leading-relaxed">Apply copper-based fungicide immediately. Remove affected leaves. Improve air circulation and avoid overhead watering.</p>
                    </div>
                </div>
            </div>
            

            <!-- Environmental Factors -->
            
            <div>
                <h5 class="font-semibold text-gray-800 mb-3">Environmental Factors</h5>
                <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <svg class="w-5 h-5 text-indigo-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"></path>
                        </svg>
                        <p class="text-indigo-800 leading-relaxed">High humidity and warm temperatures favor disease development</p>
                    </div>
                </div>
            </div>
            

            <!-- Prognosis and Urgency -->
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                
                <div class="bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <h5 class="font-semibold text-teal-800 mb-2">Prognosis</h5>
                    <p class="text-teal-700">Good with prompt treatment - disease can be controlled</p>
                </div>
                

                
                <div class="bg-orange-50 border-orange-200 border rounded-lg p-4">
                    <h5 class="font-semibold text-orange-800 mb-2">Treatment Urgency</h5>
                    <p class="text-orange-700 capitalize">
                        ⚠️ Treat Within a Week
                        
                    </p>
                </div>
                
            </div>
            

            <!-- Uploaded Image -->
            

            <!-- Action Buttons -->
            <div class="flex flex-wrap gap-3 pt-4 border-t border-gray-200">
                <button onclick="window.print()"
                        class="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                    </svg>
                    <span>Print Results</span>
                </button>

                <button onclick="shareResults()"
                        class="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                    </svg>
                    <span>Share</span>
                </button>

                <a href="/upload/"
                   class="flex items-center space-x-2 px-4 py-2 bg-tomato-600 text-white rounded-lg hover:bg-tomato-700 transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M12 4v16m8-8H4"></path>
                    </svg>
                    <span>Analyze Another</span>
                </a>
            </div>
        </div>
    </div>
    


<script>
function openImageModal(imageUrl, imageName) {
    window.dispatchEvent(new CustomEvent('open-image-modal', {
        detail: { url: imageUrl, name: imageName }
    }));
}

function shareResults() {
    if (navigator.share) {
        navigator.share({
            title: 'TomatoGuard Analysis Results',
            text: 'Check out my tomato plant analysis results from TomatoGuard AI!',
            url: window.location.href
        });
    } else {
        // Fallback for browsers that don't support Web Share API
        const url = window.location.href;
        navigator.clipboard.writeText(url).then(() => {
            alert('Results URL copied to clipboard!');
        });
    }
}
</script>
