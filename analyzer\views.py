from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse, HttpResponse
from django.contrib import messages
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views.generic import ListView, DetailView
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
import json
import time
import logging

from .models import AnalysisResult, DiseaseInfo
from .forms import ImageUploadForm, AnalysisFilterForm, FeedbackForm
from .utils.gemini import analyze_tomato_leaf
from .utils.image_processing import validate_uploaded_image, preprocess_uploaded_image

# Configure logging
logger = logging.getLogger(__name__)

def index(request):
    """Home page view."""
    # Get recent analysis statistics
    recent_analyses = AnalysisResult.objects.filter(
        created_at__gte=timezone.now() - timezone.timedelta(days=30)
    )

    stats = {
        'total_analyses': AnalysisResult.objects.count(),
        'recent_analyses': recent_analyses.count(),
        'healthy_plants': recent_analyses.filter(is_healthy=True).count(),
        'diseases_detected': recent_analyses.filter(is_healthy=False).count(),
    }

    # Get recent analysis results for display
    recent_results = AnalysisResult.objects.filter(
        error_message__isnull=True
    )[:6]

    context = {
        'stats': stats,
        'recent_results': recent_results,
    }

    return render(request, 'analyzer/index.html', context)

def upload(request):
    """Upload and analysis page."""
    form = ImageUploadForm()

    context = {
        'form': form,
    }

    return render(request, 'analyzer/upload.html', context)

@require_http_methods(["POST"])
def analyze(request):
    """Handle image analysis via HTMX."""
    start_time = time.time()

    try:
        form = ImageUploadForm(request.POST, request.FILES)

        if form.is_valid():
            # Create analysis result instance (don't save to DB yet due to SQLite JSON issue)
            analysis_result = form.save(commit=False)

            # For now, create a temporary result object that won't be saved to DB
            temp_result = type('TempResult', (), {
                'id': 'temp-analysis',
                'created_at': timezone.now(),
                'image': None,
                'analysis_type': form.cleaned_data['analysis_type'],
                'confidence_threshold': form.cleaned_data['confidence_threshold'],
                'notes': form.cleaned_data.get('notes', ''),
                'is_healthy': None,
                'disease_name': None,
                'confidence': 0,
                'severity': 'unknown',
                'description': '',
                'recommendations': '',
                'error_message': None,
                'analysis_duration': 0,
                'image_width': None,
                'image_height': None,
                'image_size': None
            })()

            # Get form data
            image_file = form.cleaned_data['image']
            analysis_type = form.cleaned_data['analysis_type']
            confidence_threshold = form.cleaned_data['confidence_threshold']

            # Store image metadata in temp result
            temp_result.image_size = image_file.size

            # Validate and preprocess image
            is_valid, error_message = validate_uploaded_image(image_file)
            if not is_valid:
                temp_result.error_message = error_message
                return render(request, 'components/analysis_results.html', {
                    'analysis_result': temp_result
                })

            # Preprocess image to get dimensions
            try:
                processed_image = preprocess_uploaded_image(image_file, enhance=False)
                temp_result.image_width = processed_image.size[0]
                temp_result.image_height = processed_image.size[1]
            except Exception as e:
                logger.warning(f"Could not get image dimensions: {str(e)}")

            # Use temp_result instead of saving to database

            # Perform AI analysis
            try:
                logger.info(f"Starting analysis for image: {image_file.name}")

                # Reset file pointer
                image_file.seek(0)

                # Analyze with Gemini
                gemini_result = analyze_tomato_leaf(
                    image_file,
                    analysis_type,
                    confidence_threshold
                )

                # Update temp result with Gemini response
                if gemini_result.get('error'):
                    temp_result.error_message = gemini_result.get('error_message', 'Analysis failed')
                else:
                    # Extract results
                    temp_result.is_healthy = gemini_result.get('is_healthy', None)
                    temp_result.disease_name = gemini_result.get('disease_detected') or gemini_result.get('disease_name')
                    temp_result.confidence = gemini_result.get('confidence', 0)
                    temp_result.severity = gemini_result.get('severity', 'unknown')
                    temp_result.description = gemini_result.get('description', '')
                    temp_result.recommendations = gemini_result.get('recommendations', '')

                # Calculate analysis duration
                temp_result.analysis_duration = time.time() - start_time

                logger.info(f"Analysis completed in {temp_result.analysis_duration:.2f} seconds")

            except Exception as e:
                logger.error(f"Error during analysis: {str(e)}")
                temp_result.error_message = f"Analysis failed: {str(e)}"
                temp_result.analysis_duration = time.time() - start_time

            # Return results via HTMX
            return render(request, 'components/analysis_results.html', {
                'analysis_result': temp_result
            })

        else:
            # Form validation failed
            logger.warning(f"Form validation failed: {form.errors}")

            # Create a simple error result for HTMX
            error_html = f"""
            <div class="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
                <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-red-800 mb-2">Upload Error</h3>
                <p class="text-red-700">Please correct the form errors and try again.</p>
                <div class="mt-4 text-sm text-red-600">
                    {'; '.join([f"{field}: {', '.join(errors)}" for field, errors in form.errors.items()])}
                </div>
            </div>
            """
            return HttpResponse(error_html)

    except Exception as e:
        logger.error(f"Unexpected error in analyze view: {str(e)}")

        # Create error HTML for HTMX
        error_html = f"""
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-red-800 mb-2">Analysis Error</h3>
            <p class="text-red-700">An unexpected error occurred. Please try again.</p>
        </div>
        """
        return HttpResponse(error_html, status=500)

def results(request, analysis_id=None):
    """Display analysis results."""
    analysis_result = None

    if analysis_id:
        analysis_result = get_object_or_404(AnalysisResult, id=analysis_id)

    context = {
        'analysis_result': analysis_result,
    }

    return render(request, 'analyzer/results.html', context)
