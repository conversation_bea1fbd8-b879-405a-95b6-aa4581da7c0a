#!/usr/bin/env python
"""
Test real upload functionality with mock Gemini API.
"""

import os
import django
from io import BytesIO
from PIL import Image

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'TomatoGuard.settings')
django.setup()

from django.test import Client
from django.core.files.uploadedfile import SimpleUploadedFile

def create_test_image():
    """Create a simple test image."""
    # Create a simple green image (simulating a leaf)
    img = Image.new('RGB', (400, 400), color='green')
    
    # Add some variation to make it look more like a leaf
    for x in range(400):
        for y in range(400):
            # Add some random variation
            if (x + y) % 30 == 0:
                img.putpixel((x, y), (0, 120, 0))  # Darker green
            elif (x + y) % 50 == 0:
                img.putpixel((x, y), (50, 150, 50))  # Lighter green
    
    # Save to BytesIO
    img_io = BytesIO()
    img.save(img_io, format='JPEG')
    img_io.seek(0)
    
    return SimpleUploadedFile(
        name='test_tomato_leaf.jpg',
        content=img_io.getvalue(),
        content_type='image/jpeg'
    )

def test_real_upload():
    """Test a real upload with mock analysis."""
    print("🍅 Testing Real Upload with Mock Analysis")
    print("=" * 50)
    
    client = Client()
    
    # Create test image
    test_image = create_test_image()
    print(f"Created test image: {test_image.name} ({len(test_image.read())} bytes)")
    test_image.seek(0)  # Reset file pointer
    
    # Prepare form data
    form_data = {
        'analysis_type': 'disease_detection',
        'confidence_threshold': 75,
        'notes': 'Test upload with mock analysis'
    }
    
    print("Submitting form...")
    
    # Submit form
    response = client.post('/analyze/', {
        **form_data,
        'image': test_image
    })
    
    print(f"Response status: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode()
        print("✅ Upload successful!")
        
        # Check for key elements in the response
        if 'Analysis Complete' in content:
            print("✅ Analysis completed successfully")
        elif 'Analysis Failed' in content or 'Analysis Error' in content:
            print("⚠️ Analysis failed but error was handled properly")
        else:
            print("⚠️ Unexpected response format")
        
        # Look for specific analysis elements
        if 'Confidence Score' in content:
            print("✅ Confidence score displayed")
        
        if 'Healthy Plant' in content or 'Disease Detected' in content:
            print("✅ Health status displayed")
        
        if 'Recommendations' in content:
            print("✅ Recommendations provided")
        
        # Save response for inspection
        with open('test_response.html', 'w', encoding='utf-8') as f:
            f.write(content)
        print("📄 Response saved to test_response.html")
        
    else:
        print(f"❌ Upload failed with status {response.status_code}")
        print(f"Response: {response.content.decode()[:500]}...")

def test_upload_page():
    """Test that the upload page loads."""
    print("\n🌐 Testing Upload Page")
    print("=" * 30)
    
    client = Client()
    response = client.get('/upload/')
    
    if response.status_code == 200:
        print("✅ Upload page loads successfully")
        
        content = response.content.decode()
        
        # Check for key elements
        if 'Upload Tomato Leaf Image' in content:
            print("✅ Upload form present")
        
        if 'Analyze Image' in content:
            print("✅ Submit button present")
        
        if 'analysis-results' in content:
            print("✅ Results container present")
        
    else:
        print(f"❌ Upload page failed: {response.status_code}")

if __name__ == '__main__':
    test_upload_page()
    test_real_upload()
    print("\n🎉 Testing complete!")
